"""
API Views pour Kaydan Analytics Hub
Gestion des endpoints REST pour tous les modèles de données
Version propre et organisée
"""

# ========================================
# IMPORTS DJANGO ET DRF
# ========================================
from django.shortcuts import render, get_object_or_404
from django.db import models
from rest_framework import viewsets, permissions, status
from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework.viewsets import ViewSet
from knox.auth import TokenAuthentication
import requests
from datetime import datetime

# ========================================
# IMPORTS MODÈLES
# ========================================
from .models import (
    # Modèles utilisateur
    UserProfile,
    
    # Modèles de données externes existants
    DQEData,
    GStockApprovisionnement,
    GStockSortie,
    GStockConsommation,
    GStockAchat,
    GProjet,
    EcoleTalents,
    GLocative,
    ESyndic,
    Sentinelle,
    
    # Nouveaux modèles de données externes
    DonneesProduitInterieurBrut,
    donneesInflation,
    DonnesTauxPretImmobilier,
    DonneesPrixMetreCarre,
    DonneesMateriauxConstruction,
    DonneesProjectionDemographique,
    DonneesMigrationInterne,
)

from accounts.models import CustomUser

# ========================================
# IMPORTS SERIALIZERS
# ========================================
from .serializers import (
    # Serializers utilisateur
    UserProfileSerializer,
    UserProfileUpdateSerializer,
    
    # Serializers données externes existants
    DQEDataSerializer,
    GStockApprovisionnementSerializer,
    GStockSortieSerializer,
    GStockConsommationSerializer,
    GStockAchatSerializer,
    GProjetSerializer,
    EcoleTalentSerializer,
    GLocativeSerializer,
    ESyndicSerializer,
    SentinelleSerializer,
    
    # Nouveaux serializers
    DonneesProduitInterieurBrutSerializer,
    DonneesInflationSerializer,
    DonnesTauxPretImmobilierSerializer,
    DonneesPrixMetreCarreSerializer,
    DonneesMateriauxConstructionSerializer,
    DonneesProjectionDemographiqueSerializer,
    DonneesMigrationInterneSerializer,
)


# ========================================
# VIEWSETS UTILISATEUR
# ========================================

class UserProfileViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour la gestion des profils utilisateur
    """
    queryset = UserProfile.objects.all()
    serializer_class = UserProfileSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """Filtrer par utilisateur connecté"""
        return UserProfile.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        """Associer le profil à l'utilisateur connecté"""
        serializer.save(user=self.request.user)

    @action(detail=False, methods=['get'])
    def me(self, request):
        """Récupère le profil de l'utilisateur connecté"""
        try:
            profile = UserProfile.objects.get(user=request.user)
            serializer = self.get_serializer(profile)
            return Response(serializer.data)
        except UserProfile.DoesNotExist:
            return Response(
                {"error": "Profil non trouvé"}, 
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=False, methods=['put', 'patch'])
    def update_profile(self, request):
        """Met à jour le profil de l'utilisateur connecté"""
        try:
            profile = UserProfile.objects.get(user=request.user)
            serializer = UserProfileUpdateSerializer(
                profile, 
                data=request.data, 
                partial=request.method == 'PATCH'
            )
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except UserProfile.DoesNotExist:
            return Response(
                {"error": "Profil non trouvé"}, 
                status=status.HTTP_404_NOT_FOUND
            )


# ========================================
# VIEWSETS DONNÉES EXTERNES EXISTANTES
# ========================================

class KreDQEData(ViewSet):
    """
    ViewSet pour les données DQE (Devis Quantitatif Estimatif)
    """
    permission_classes = [permissions.IsAuthenticated]

    def list(self, request):
        """Récupère et sauvegarde les données DQE depuis l'API externe"""
        try:
            # URL de l'API externe DQE
            api_url = "http://*************:8000/api/dqes/getData-to-kah"
            response = requests.get(api_url, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                # Sauvegarder en base
                dqe_entry = DQEData.objects.create(
                    title="DQE Data Import",
                    description=f"Import automatique du {datetime.now()}",
                    data=data
                )
                
                serializer = DQEDataSerializer(dqe_entry)
                return Response({
                    "status": "success",
                    "message": "Données DQE récupérées et sauvegardées",
                    "data": serializer.data
                })
            else:
                return Response({
                    "status": "error",
                    "message": f"Erreur API externe: {response.status_code}"
                }, status=status.HTTP_502_BAD_GATEWAY)
                
        except requests.RequestException as e:
            return Response({
                "status": "error",
                "message": f"Erreur de connexion: {str(e)}"
            }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
        except Exception as e:
            return Response({
                "status": "error",
                "message": f"Erreur interne: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def get_latest(self, request):
        """Récupère les dernières données DQE de la base"""
        try:
            latest_data = DQEData.objects.latest("created_at")
            serializer = DQEDataSerializer(latest_data)
            return Response({
                "status": "success",
                "data": serializer.data
            })
        except DQEData.DoesNotExist:
            return Response({
                "status": "error",
                "message": "Aucune donnée DQE trouvée"
            }, status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['get'])
    def list_all(self, request):
        """Liste toutes les données DQE"""
        data = DQEData.objects.all().order_by('-created_at')
        serializer = DQEDataSerializer(data, many=True)
        return Response({
            "status": "success",
            "count": len(data),
            "data": serializer.data
        })


# ========================================
# VIEWSETS G-STOCK (Gestion de Stock)
# ========================================

class GStockApprovisionnementViewSet(ViewSet):
    """
    ViewSet pour les données d'approvisionnement G-Stock
    """
    permission_classes = [permissions.IsAuthenticated]

    def list(self, request):
        """Récupère les données d'approvisionnement depuis G-Stock"""
        try:
            api_url = "https://gstock.artemisconstruction-ci.com/params/get-appro"
            response = requests.get(api_url, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                # Sauvegarder en base
                appro_entry = GStockApprovisionnement.objects.create(
                    title="G-Stock Approvisionnement",
                    description=f"Import automatique du {datetime.now()}",
                    data=data
                )
                
                serializer = GStockApprovisionnementSerializer(appro_entry)
                return Response({
                    "status": "success",
                    "message": "Données d'approvisionnement récupérées",
                    "data": serializer.data
                })
            else:
                return Response({
                    "status": "error",
                    "message": f"Erreur API G-Stock: {response.status_code}"
                }, status=status.HTTP_502_BAD_GATEWAY)
                
        except Exception as e:
            return Response({
                "status": "error",
                "message": f"Erreur: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def get_latest(self, request):
        """Récupère les dernières données d'approvisionnement"""
        try:
            latest_data = GStockApprovisionnement.objects.latest("created_at")
            serializer = GStockApprovisionnementSerializer(latest_data)
            return Response({
                "status": "success",
                "data": serializer.data
            })
        except GStockApprovisionnement.DoesNotExist:
            return Response({
                "status": "error",
                "message": "Aucune donnée d'approvisionnement trouvée"
            }, status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['get'])
    def list_all(self, request):
        """Liste toutes les données d'approvisionnement"""
        data = GStockApprovisionnement.objects.all().order_by('-created_at')
        serializer = GStockApprovisionnementSerializer(data, many=True)
        return Response({
            "status": "success",
            "count": len(data),
            "data": serializer.data
        })


# ========================================
# VIEWSETS ECOLE DES TALENTS
# ========================================

class EcoleTalentsViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour la gestion des données Ecole des Talents
    """

    permission_classes = [permissions.IsAuthenticated]

    def list(self, request):
        """Récupère et sauvegarde les données Ecole des Talents depuis l'API externe"""
        try:
            # URL de l'API externe Ecole des Talents
            api_url = "https://talents.kaydangroupe.com/params/data-to-kah"
            response = requests.get(api_url, timeout=30)

            if response.status_code == 200:
                data = response.json()

                # Sauvegarder en base
                talents_entry = EcoleTalents.objects.create(
                    title="Ecole des Talents Data Import",
                    description=f"Import automatique du {datetime.now()}",
                    data=data,
                )

                serializer = EcoleTalentSerializer(talents_entry)
                return Response(
                    {
                        "status": "success",
                        "message": "Données Ecole des Talents récupérées et sauvegardées",
                        "data": serializer.data,
                    }
                )
            else:
                return Response(
                    {
                        "status": "error",
                        "message": f"Erreur API externe: {response.status_code}",
                    },
                    status=status.HTTP_502_BAD_GATEWAY,
                )

        except requests.RequestException as e:
            return Response(
                {"status": "error", "message": f"Erreur de connexion: {str(e)}"},
                status=status.HTTP_503_SERVICE_UNAVAILABLE,
            )
        except Exception as e:
            return Response(
                {"status": "error", "message": f"Erreur interne: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["get"])
    def get_latest(self, request):
        """Récupère les dernières données Ecole des Talents de la base"""
        try:
            latest_data = EcoleTalents.objects.latest("created_at")
            serializer = EcoleTalentSerializer(latest_data)
            return Response({"status": "success", "data": serializer.data})
        except EcoleTalents.DoesNotExist:
            return Response(
                {
                    "status": "error",
                    "message": "Aucune donnée Ecole des Talents trouvée",
                },
                status=status.HTTP_404_NOT_FOUND,
            )

    @action(detail=False, methods=["get"])
    def list_all(self, request):
        """Liste toutes les données Ecole des Talents"""
        data = EcoleTalents.objects.all().order_by("-created_at")
        serializer = EcoleTalentSerializer(data, many=True)
        return Response(
            {"status": "success", "count": len(data), "data": serializer.data}
        )


# ========================================
# VIEWSETS G_LOCATIVE (Gestion de Locative)
# ========================================

class KreGLocativeData(ViewSet):
    """
    ViewSet pour les données G-Locative
    """
    permission_classes = [permissions.IsAuthenticated]

    def list(self, request):
        """Récupère et sauvegarde les données G-Locative depuis l'API externe"""
        try:
            # URL de l'API externe G-Locative
            api_url = "https://locative.inoovim.com/api/listdata"
            response = requests.get(api_url, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                # Sauvegarder en base
                locative_entry = GLocative.objects.create(
                    title="GLocative Data Import",
                    description=f"Import automatique du {datetime.now()}",
                    data=data
                )
                
                serializer = GLocativeSerializer(locative_entry)
                return Response({
                    "status": "success",
                    "message": "Données G-Locative récupérées et sauvegardées",
                    "data": serializer.data
                })
            else:
                return Response({
                    "status": "error",
                    "message": f"Erreur API externe: {response.status_code}"
                }, status=status.HTTP_502_BAD_GATEWAY)
                
        except requests.RequestException as e:
            return Response({
                "status": "error",
                "message": f"Erreur de connexion: {str(e)}"
            }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
        except Exception as e:
            return Response({
                "status": "error",
                "message": f"Erreur interne: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def get_latest(self, request):
        """Récupère les dernières données G-Locative de la base"""
        try:
            latest_data = GLocative.objects.latest("created_at")
            serializer = GLocativeSerializer(latest_data)
            return Response({
                "status": "success",
                "data": serializer.data
            })
        except GLocative.DoesNotExist:
            return Response({
                "status": "error",
                "message": "Aucune donnée G-Locative trouvée"
            }, status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['get'])
    def list_all(self, request):
        """Liste toutes les données G-Locative"""
        data = GLocative.objects.all().order_by('-created_at')
        serializer = GLocativeSerializer(data, many=True)
        return Response({
            "status": "success",
            "count": len(data),
            "data": serializer.data
        })


# ========================================
# VIEWSETS E_SYNDIC (Gestion de Syndic)
# ========================================


class KreESyndicData(ViewSet):
    """
    ViewSet pour les données E-Syndic
    """
    permission_classes = [permissions.IsAuthenticated]

    def list(self, request):
        """Récupère et sauvegarde les données E-Syndic depuis l'API externe"""
        try:
            # URL de l'API externe E-Syndic
            api_url = "https://e-syndic.inoovim.com/api/listdata"
            response = requests.get(api_url, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                # Sauvegarder en base
                syndic_entry = ESyndic.objects.create(
                    title="ESyndic Data Import",
                    description=f"Import automatique du {datetime.now()}",
                    data=data
                )
                
                serializer = ESyndicSerializer(syndic_entry)
                return Response({
                    "status": "success",
                    "message": "Données E-Syndic récupérées et sauvegardées",
                    "data": serializer.data
                })
            else:
                return Response({
                    "status": "error",
                    "message": f"Erreur API externe: {response.status_code}"
                }, status=status.HTTP_502_BAD_GATEWAY)
                
        except requests.RequestException as e:
            return Response({
                "status": "error",
                "message": f"Erreur de connexion: {str(e)}"
            }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
        except Exception as e:
            return Response({
                "status": "error",
                "message": f"Erreur interne: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def get_latest(self, request):
        """Récupère les dernières données E-Syndic de la base"""
        try:
            latest_data = ESyndic.objects.latest("created_at")
            serializer = ESyndicSerializer(latest_data)
            return Response({
                "status": "success",
                "data": serializer.data
            })
        except ESyndic.DoesNotExist:
            return Response({
                "status": "error",
                "message": "Aucune donnée E-Syndic trouvée"
            }, status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['get'])
    def list_all(self, request):
        """Liste toutes les données E-Syndic"""
        data = ESyndic.objects.all().order_by('-created_at')
        serializer = ESyndicSerializer(data, many=True)
        return Response({
            "status": "success",
            "count": len(data),
            "data": serializer.data
        })

# ========================================
# VIEWSETS SENTINELLE
# ========================================


class KreSentinelleData(ViewSet):
    """
    ViewSet pour les données Sentinelle avec authentification Basic et API Key
    """
    permission_classes = [permissions.IsAuthenticated]

    def list(self, request):
        """Récupère et sauvegarde les données Sentinelle depuis l'API externe"""
        try:
            # URL de l'API externe Sentinelle
            api_url = "http://sentinelle.kaydanrealestate.com:8181/api/services/taux_programme"
            headers = {
                'Authorization': 'Basic e3t1c2VyfX06e3twd2R9fQ==',
                'X-API-KEY': '5632d76ece5711436d3084a628f2afb03388a373'
            }
            response = requests.get(api_url, headers=headers, timeout=30)

            if response.status_code == 200:
                data = response.json()

                # Sauvegarder en base
                sentinelle_entry = Sentinelle.objects.create(
                    title="Sentinelle Data Import",
                    description=f"Import automatique du {datetime.now()}",
                    data=data
                )

                serializer = SentinelleSerializer(sentinelle_entry)
                return Response({
                    "status": "success",
                    "message": "Données Sentinelle récupérées et sauvegardées",
                    "data": serializer.data
                })
            elif response.status_code == 401:
                return Response({
                    "status": "error",
                    "message": "Erreur d'authentification - Vérifiez les credentials"
                }, status=status.HTTP_502_BAD_GATEWAY)
            elif response.status_code == 403:
                return Response({
                    "status": "error",
                    "message": "Accès refusé - Vérifiez l'API Key"
                }, status=status.HTTP_502_BAD_GATEWAY)
            else:
                return Response({
                    "status": "error",
                    "message": f"Erreur API externe: {response.status_code} - {response.text}"
                }, status=status.HTTP_502_BAD_GATEWAY)

        except requests.RequestException as e:
            return Response({
                "status": "error",
                "message": f"Erreur de connexion: {str(e)}"
            }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
        except Exception as e:
            return Response({
                "status": "error",
                "message": f"Erreur interne: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


    @action(detail=False, methods=['get'])
    def get_latest(self, request):
        """Récupère les dernières données Sentinelle de la base"""
        try:
            latest_data = Sentinelle.objects.latest("created_at")
            serializer = SentinelleSerializer(latest_data)
            return Response({
                "status": "success",
                "data": serializer.data
            })
        except Sentinelle.DoesNotExist:
            return Response({
                "status": "error",
                "message": "Aucune donnée Sentinelle trouvée"
            }, status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['get'])
    def list_all(self, request):
        """Liste toutes les données Sentinelle"""
        data = Sentinelle.objects.all().order_by('-created_at')
        serializer = SentinelleSerializer(data, many=True)
        return Response({
            "status": "success",
            "count": len(data),
            "data": serializer.data
        })


# ========================================
# VIEWSETS NOUVEAUX MODÈLES DE DONNÉES
# ========================================

class DonneesProduitInterieurBrutViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour la gestion des données PIB (Produit Intérieur Brut)
    Fournit les opérations CRUD complètes : GET, POST, PUT, PATCH, DELETE
    """
    queryset = DonneesProduitInterieurBrut.objects.all()
    serializer_class = DonneesProduitInterieurBrutSerializer
    permission_classes = [permissions.IsAuthenticated]
    authentication_classes = [TokenAuthentication]

    @action(detail=False, methods=['get'])
    def latest(self, request):
        """Récupère la dernière donnée PIB"""
        try:
            latest = self.queryset.latest('created_at')
            serializer = self.get_serializer(latest)
            return Response(serializer.data)
        except DonneesProduitInterieurBrut.DoesNotExist:
            return Response(
                {"error": "Aucune donnée PIB trouvée"},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=False, methods=['get'])
    def by_year(self, request):
        """Filtre les données PIB par année"""
        annee = request.query_params.get('annee')
        if not annee:
            return Response(
                {"error": "Le paramètre 'annee' est requis"},
                status=status.HTTP_400_BAD_REQUEST
            )

        data = self.queryset.filter(annee_deb_couv=annee)
        serializer = self.get_serializer(data, many=True)
        return Response(serializer.data)


class DonneesInflationViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour la gestion des données d'inflation
    """
    queryset = donneesInflation.objects.all()
    serializer_class = DonneesInflationSerializer
    permission_classes = [permissions.IsAuthenticated]
    authentication_classes = [TokenAuthentication]

    @action(detail=False, methods=['get'])
    def by_country(self, request):
        """Filtre les données d'inflation par pays"""
        country_code = request.query_params.get('country_code')
        if not country_code:
            return Response(
                {"error": "Le paramètre 'country_code' est requis"},
                status=status.HTTP_400_BAD_REQUEST
            )

        data = self.queryset.filter(country_code=country_code)
        serializer = self.get_serializer(data, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def latest_by_country(self, request):
        """Récupère la dernière donnée d'inflation par pays"""
        country_code = request.query_params.get('country_code')
        if not country_code:
            return Response(
                {"error": "Le paramètre 'country_code' est requis"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            latest = self.queryset.filter(country_code=country_code).latest('year')
            serializer = self.get_serializer(latest)
            return Response(serializer.data)
        except donneesInflation.DoesNotExist:
            return Response(
                {"error": f"Aucune donnée d'inflation trouvée pour {country_code}"},
                status=status.HTTP_404_NOT_FOUND
            )


class DonnesTauxPretImmobilierViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour la gestion des taux de prêt immobilier
    """
    queryset = DonnesTauxPretImmobilier.objects.all()
    serializer_class = DonnesTauxPretImmobilierSerializer
    permission_classes = [permissions.IsAuthenticated]
    authentication_classes = [TokenAuthentication]

    @action(detail=False, methods=['get'])
    def by_bank(self, request):
        """Filtre les taux par banque"""
        banque = request.query_params.get('banque')
        if not banque:
            return Response(
                {"error": "Le paramètre 'banque' est requis"},
                status=status.HTTP_400_BAD_REQUEST
            )

        data = self.queryset.filter(banque__icontains=banque)
        serializer = self.get_serializer(data, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def by_loan_type(self, request):
        """Filtre les taux par type de prêt"""
        type_pret = request.query_params.get('type_pret')
        if not type_pret:
            return Response(
                {"error": "Le paramètre 'type_pret' est requis"},
                status=status.HTTP_400_BAD_REQUEST
            )

        data = self.queryset.filter(type_pret__icontains=type_pret)
        serializer = self.get_serializer(data, many=True)
        return Response(serializer.data)


class DonneesPrixMetreCarreViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour la gestion des prix au mètre carré
    """
    queryset = DonneesPrixMetreCarre.objects.all()
    serializer_class = DonneesPrixMetreCarreSerializer
    permission_classes = [permissions.IsAuthenticated]
    authentication_classes = [TokenAuthentication]

    @action(detail=False, methods=['get'])
    def by_commune(self, request):
        """Filtre les prix par commune"""
        commune = request.query_params.get('commune')
        if not commune:
            return Response(
                {"error": "Le paramètre 'commune' est requis"},
                status=status.HTTP_400_BAD_REQUEST
            )

        data = self.queryset.filter(commune__icontains=commune)
        serializer = self.get_serializer(data, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def price_range(self, request):
        """Filtre les prix par plage de prix"""
        prix_min = request.query_params.get('prix_min')
        prix_max = request.query_params.get('prix_max')

        if not prix_min or not prix_max:
            return Response(
                {"error": "Les paramètres 'prix_min' et 'prix_max' sont requis"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            prix_min = float(prix_min)
            prix_max = float(prix_max)
        except ValueError:
            return Response(
                {"error": "Les prix doivent être des nombres valides"},
                status=status.HTTP_400_BAD_REQUEST
            )

        data = self.queryset.filter(
            prix_moyen__gte=prix_min,
            prix_moyen__lte=prix_max
        )
        serializer = self.get_serializer(data, many=True)
        return Response(serializer.data)


class DonneesMateriauxConstructionViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour la gestion des matériaux de construction
    """
    queryset = DonneesMateriauxConstruction.objects.all()
    serializer_class = DonneesMateriauxConstructionSerializer
    permission_classes = [permissions.IsAuthenticated]
    authentication_classes = [TokenAuthentication]

    @action(detail=False, methods=['get'])
    def by_category(self, request):
        """Filtre les matériaux par catégorie"""
        categorie = request.query_params.get('categorie')
        if not categorie:
            return Response(
                {"error": "Le paramètre 'categorie' est requis"},
                status=status.HTTP_400_BAD_REQUEST
            )

        data = self.queryset.filter(categorie__icontains=categorie)
        serializer = self.get_serializer(data, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def search(self, request):
        """Recherche dans les matériaux par titre ou description"""
        q = request.query_params.get('q')
        if not q:
            return Response(
                {"error": "Le paramètre de recherche 'q' est requis"},
                status=status.HTTP_400_BAD_REQUEST
            )

        data = self.queryset.filter(
            models.Q(titre__icontains=q) |
            models.Q(description__icontains=q)
        )
        serializer = self.get_serializer(data, many=True)
        return Response(serializer.data)


class DonneesProjectionDemographiqueViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour la gestion des projections démographiques
    """
    queryset = DonneesProjectionDemographique.objects.all()
    serializer_class = DonneesProjectionDemographiqueSerializer
    permission_classes = [permissions.IsAuthenticated]
    authentication_classes = [TokenAuthentication]

    @action(detail=False, methods=['get'])
    def by_region(self, request):
        """Filtre les projections par région"""
        region = request.query_params.get('region')
        if not region:
            return Response(
                {"error": "Le paramètre 'region' est requis"},
                status=status.HTTP_400_BAD_REQUEST
            )

        data = self.queryset.filter(region__icontains=region)
        serializer = self.get_serializer(data, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def latest_by_region(self, request):
        """Récupère la dernière projection par région"""
        region = request.query_params.get('region')
        if not region:
            return Response(
                {"error": "Le paramètre 'region' est requis"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            latest = self.queryset.filter(region__icontains=region).latest('annee')
            serializer = self.get_serializer(latest)
            return Response(serializer.data)
        except DonneesProjectionDemographique.DoesNotExist:
            return Response(
                {"error": f"Aucune projection trouvée pour la région {region}"},
                status=status.HTTP_404_NOT_FOUND
            )


class DonneesMigrationInterneViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour la gestion des données de migration interne
    """
    queryset = DonneesMigrationInterne.objects.all()
    serializer_class = DonneesMigrationInterneSerializer
    permission_classes = [permissions.IsAuthenticated]
    authentication_classes = [TokenAuthentication]

    @action(detail=False, methods=['get'])
    def by_origin(self, request):
        """Filtre les migrations par région d'origine"""
        region_origine = request.query_params.get('region_origine')
        if not region_origine:
            return Response(
                {"error": "Le paramètre 'region_origine' est requis"},
                status=status.HTTP_400_BAD_REQUEST
            )

        data = self.queryset.filter(region_origine__icontains=region_origine)
        serializer = self.get_serializer(data, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def by_destination(self, request):
        """Filtre les migrations par région de destination"""
        region_destination = request.query_params.get('region_destination')
        if not region_destination:
            return Response(
                {"error": "Le paramètre 'region_destination' est requis"},
                status=status.HTTP_400_BAD_REQUEST
            )

        data = self.queryset.filter(region_destination__icontains=region_destination)
        serializer = self.get_serializer(data, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def top_migrations(self, request):
        """Récupère les top migrations par nombre de migrants"""
        limit = request.query_params.get('limit', 10)
        try:
            limit = int(limit)
        except ValueError:
            limit = 10

        data = self.queryset.order_by('-nombre_migrants_total')[:limit]
        serializer = self.get_serializer(data, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def migration_flows(self, request):
        """Analyse des flux migratoires par région"""
        # Agrégation des données par région d'origine et destination
        from django.db.models import Sum

        flows = self.queryset.values(
            'region_origine',
            'region_destination'
        ).annotate(
            total_migrants=Sum('nombre_migrants_total')
        ).order_by('-total_migrants')

        return Response({
            "status": "success",
            "count": len(flows),
            "data": list(flows)
        })
